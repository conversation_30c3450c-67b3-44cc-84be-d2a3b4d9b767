import 'dart:io';

import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_direct_caller_plugin/flutter_direct_caller_plugin.dart';

// import 'package:flutter_phone_direct_caller/flutter_phone_direct_caller.dart';
import 'package:image_picker/image_picker.dart' as picker;
import 'package:provider/provider.dart';
import 'package:universal_html/html.dart' as html;

import '../../../common/config.dart';
import '../../../common/constants.dart';
import '../../../common/tools.dart';
import '../../../frameworks/strapi/services/strapi_service.dart';
import '../../../generated/l10n.dart';
import '../../../models/entities/payment_method.dart';
import '../../../models/entities/product.dart';
import '../../../models/index.dart'
    show App<PERSON>ode<PERSON>, CartModel, PaymentMethodModel, TaxModel;
import '../../../modules/dynamic_layout/helper/helper.dart';
import '../../../services/index.dart';
import '../../../widgets/common/common_safe_area.dart';
import '../../cart/widgets/shopping_cart_sumary.dart';
import '../../review/views/create_review_screen.dart';
import '../mixins/checkout_mixin.dart';

// import 'package:flutter_phone_direct_caller/flutter_phone_direct_caller.dart';
import 'payment_method_item.dart';

final attachedPaymentScreenshot = ValueNotifier<File?>(null);
final webAttachedPaymentScreenshot = ValueNotifier<html.File?>(null);

class PaymentMethods extends StatefulWidget {
  final Function? onBack;
  final Function? onFinish;
  final Function(bool)? onLoading;
  final bool hideCheckout;

  const PaymentMethods({
    this.onBack,
    this.onFinish,
    this.onLoading,
    this.hideCheckout = false,
  });

  @override
  State<PaymentMethods> createState() => _PaymentMethodsState();
}

class _PaymentMethodsState extends State<PaymentMethods> with CheckoutMixin {
  @override
  Function? get onBack => widget.onBack;

  @override
  Function? get onFinish => widget.onFinish;

  @override
  Function(bool)? get onLoading => widget.onLoading;

  // Future<void> _pickImage() async {
  //   final permissionState = await ImagePicker.checkGrantedPermission();
  //   if (permissionState.isDenied) {
  //     ImagePicker.showDialogRequestPermission(context);
  //     return;
  //   }
  //
  //   final selectedAssets = attachedPaymentScreenshot.value;
  //
  //   final newSelected = await ImagePicker.select(
  //     context,
  //     selectedAssets: selectedAssets,
  //   );
  //
  //   await Future.delayed(const Duration(milliseconds: 500));
  //
  //   attachedPaymentScreenshot.value =
  //       newSelected.isNotEmpty ? newSelected : selectedAssets;
  //
  //   printLog('SelectedAssets: $newSelected');
  //
  //   setState(() {});
  // }

  Future<void> _pickImage() async {
    if (kIsWeb) {
      final input = html.FileUploadInputElement()..accept = 'image/*';
      input.click();

      input.onChange.listen((event) {
        final file = input.files!.first;
        webAttachedPaymentScreenshot.value = file;
        setState(() {});
      });
    } else {
      final pick = picker.ImagePicker();
      final pickedFile =
          await pick.pickImage(source: picker.ImageSource.gallery);

      if (pickedFile != null) {
        final imageFile = File(pickedFile.path);
        attachedPaymentScreenshot.value = imageFile;
        printLog('SelectedAssets: $imageFile');
        setState(() {});
      } else {
        printLog('No image selected.');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final cartModel = Provider.of<CartModel>(context);
    final currencyRate = Provider.of<AppModel>(context).currencyRate;
    final currency = context.read<CartModel>().currencyCode;
    final paymentMethodModel = Provider.of<PaymentMethodModel>(context);
    final taxModel = Provider.of<TaxModel>(context);
    final useDesktopLayout = Layout.isDisplayDesktop(context);

    final body = Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: 8.0,
        vertical: 8.0,
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(S.of(context).paymentMethods,
                style: const TextStyle(fontSize: 16)),
            const SizedBox(height: 5),
            Text(
              S.of(context).chooseYourPaymentMethod,
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).colorScheme.secondary.withOpacity(0.6),
              ),
            ),
            Services().widget.renderPayByWallet(context),
            const SizedBox(height: 20),
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 8.0,
              ),
              child: Consumer<PaymentMethodModel>(
                  builder: (context, model, child) {
                if (model.isLoading) {
                  return SizedBox(height: 100, child: kLoadingWidget(context));
                }

                if (model.message != null) {
                  return SizedBox(
                    height: 100,
                    child: Center(
                        child: Text(model.message!,
                            style: const TextStyle(color: kErrorRed))),
                  );
                }
                if (paymentMethodModel.paymentMethods.isEmpty) {
                  return Center(
                    child: Image.asset(
                      'assets/images/leaves.png',
                      width: 120,
                      height: 120,
                      fit: BoxFit.contain,
                    ),
                  );
                }

                selectedPaymentMethod ??=
                    paymentMethodModel.paymentMethods.firstOrNull?.title;

                if (selectedId == null && model.paymentMethods.isNotEmpty) {
                  selectedId = model.paymentMethods.firstWhereOrNull((item) {
                    return item.enabled!;
                  })?.id;
                  cartModel.setPaymentMethod(model.paymentMethods
                      .firstWhere((item) => item.id == selectedId));
                }

                Widget payWidget({required PaymentMethod paymentMethod}) {
                  //TODO-InstapayLink
                  final isInstapayLink = paymentMethod.description != null &&
                      (paymentMethod.description?.contains('http') == true ||
                          paymentMethod.description?.contains('https') == true);

                  final isInstapayButNotLink =
                      paymentMethod.title?.toLowerCase() == 'instapay' &&
                          !isInstapayLink;

                  if (isInstapayButNotLink) {
                    return const SizedBox.shrink();
                  }

                  void walletPay() async {
                    final totalAmount = cartModel.getTotal();

                    if (isInstapayLink) {
                      await Tools.launchURL(
                        paymentMethod.description!,
                      );

                      return;
                    }

                    var number = '';

                    // TODO-VodafoneCashPay
                    if (selectedPaymentMethod?.toLowerCase() ==
                        'vodafone cash') {
                      number =
                          '*9*7*${paymentMethod.description}*$totalAmount#';
                    } else if (selectedPaymentMethod?.toLowerCase() ==
                        'etisalat cash') {
                      number = '*777*1#';
                    } else if (selectedPaymentMethod?.toLowerCase() ==
                        'orange cash') {
                      number = '#7115#';
                    } else if (selectedPaymentMethod?.toLowerCase() ==
                        'we pay') {
                      number =
                          '*551*${paymentMethod.description}*$totalAmount#';
                    }

                    if (kIsWeb) {
                      final url = 'tel:$number';
                      html.window.open(url, 'Payment');
                    } else {
                      await FlutterDirectCallerPlugin.callNumber(number);
                    }
                  }

                  return Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(5),
                    ),
                    padding: const EdgeInsets.all(5),
                    child: TextButton(
                        // shape: RoundedRectangleBorder(
                        //     borderRadius: BorderRadius.circular(5)),
                        onPressed: () {
                          walletPay();
                        },
                        child: Column(
                          children: [
                            const Icon(
                              CupertinoIcons.creditcard,
                              color: Colors.black,
                            ),
                            const SizedBox(height: 5),
                            Text(
                              S.of(context).pay,
                              style: Theme.of(context)
                                  .textTheme
                                  .labelLarge!
                                  .copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black,
                                  ),
                            ),
                          ],
                        )),
                  );
                }

                final attachment = attachedPaymentScreenshot.value;
                final webAttachment = webAttachedPaymentScreenshot.value;

                final isCOD = isCODOrOnlinePayment(selectedPaymentMethod);

                return Column(
                  children: <Widget>[
                    //! if choose any method except COD (Show row -> Text - attach button) Attach your payment screenshot
                    if (!isCOD)
                      Padding(
                        padding: const EdgeInsets.only(
                          bottom: 12,
                          right: 4,
                          left: 4,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: <Widget>[
                            Expanded(
                              child: Text(
                                S.of(context).pleaseAttachYourPaymentScreenshot,
                                style: TextStyle(
                                    fontSize: 14,
                                    color: Theme.of(context)
                                        .colorScheme
                                        .secondary
                                        .withOpacity(0.8),
                                    fontWeight: FontWeight.bold),
                              ),
                            ),
                            TextButton(
                              onPressed: () {
                                _pickImage();
                                // Services().widget.showPaymentScreenshotDialog(
                                //     context, cartModel);
                              },
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.attach_file,
                                    color: Theme.of(context).primaryColor,
                                  ),
                                  Text(
                                    S.of(context).attach,
                                    style: TextStyle(
                                      color: Theme.of(context).primaryColor,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),

                    // View image attachment
                    if (!isCOD) ...[
                      if (kIsWeb && webAttachment != null) ...[
                        SizedBox(
                          height: 150,
                          child: ValueListenableBuilder(
                            valueListenable: webAttachedPaymentScreenshot,
                            builder: (BuildContext context, html.File? value,
                                Widget? child) {
                              if (value == null) {
                                return const SizedBox();
                              }

                              // Create an `ObjectUrl` from the selected file
                              final objectUrl =
                                  html.Url.createObjectUrlFromBlob(value);

                              return ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: Stack(
                                    children: [
                                      Image.network(objectUrl),
                                      Positioned.directional(
                                        top: 2,
                                        end: 10,
                                        height: 36,
                                        width: 22,
                                        textDirection:
                                            Directionality.of(context),
                                        child: ElevatedButton(
                                          style: ElevatedButton.styleFrom(
                                            shape: const CircleBorder(),
                                            padding: const EdgeInsets.all(0),
                                            backgroundColor: Theme.of(context)
                                                .colorScheme
                                                .surface,
                                          ),
                                          onPressed: () {
                                            webAttachedPaymentScreenshot.value =
                                                null;
                                          },
                                          child: Icon(
                                            Icons.close,
                                            color: Theme.of(context)
                                                .colorScheme
                                                .inverseSurface,
                                            size: 16,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ));
                            },
                          ),
                          // AssetImageWidget(
                          //   key: ValueKey(webAttachment),
                          //   filePath: webAttachment.name,
                          //   onRemove: () {
                          //     webAttachedPaymentScreenshot.value = null;
                          //
                          //     setState(() {});
                          //   },
                          // ),
                        ),
                        const SizedBox(
                          height: 20,
                        ),
                      ],
                      if (!kIsWeb && attachment != null) ...[
                        SizedBox(
                          height: 150,
                          child: AssetImageWidget(
                            key: ValueKey(attachment.path),
                            filePath: attachment.path,
                            onRemove: () {
                              attachedPaymentScreenshot.value = null;

                              setState(() {});
                            },
                          ),
                        ),
                        const SizedBox(
                          height: 20,
                        ),
                      ],
                    ],
                    for (int i = 0; i < model.paymentMethods.length; i++)
                      model.paymentMethods[i].enabled!
                          ? Services().widget.renderPaymentMethodItem(
                              context,
                              model.paymentMethods[i],
                              (i) {
                                setState(() {
                                  selectedId = i;
                                  selectedPaymentMethod = model.paymentMethods
                                      .firstWhereOrNull((item) => item.id == i)
                                      ?.title;
                                });
                                final paymentMethod = paymentMethodModel
                                    .paymentMethods
                                    .firstWhere((item) => item.id == i);
                                cartModel.setPaymentMethod(paymentMethod);
                              },
                              selectedId,
                              descWidget: !isCODOrOnlinePayment(
                                      model.paymentMethods[i].title)
                                  ? payWidget(
                                      paymentMethod: model.paymentMethods[i])
                                  : const SizedBox(),
                              useDesktopStyle: useDesktopLayout,
                            )
                          : const SizedBox()
                  ],
                );
              }),
            ),
            if (widget.hideCheckout == false) ...[
              const ShoppingCartSummary(showPrice: false),
              const SizedBox(height: 20),
              Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    Text(
                      S.of(context).subtotal,
                      style: TextStyle(
                        fontSize: 14,
                        color: Theme.of(context)
                            .colorScheme
                            .secondary
                            .withOpacity(0.8),
                      ),
                    ),
                    Text(
                        PriceTools.getCurrencyFormatted(
                            cartModel.getSubTotal(), currencyRate,
                            currency: cartModel.currencyCode)!,
                        style: const TextStyle(fontSize: 14, color: kGrey400))
                  ],
                ),
              ),
              Services().widget.renderShippingMethodInfo(context),
              if (cartModel.getCoupon() != '')
                Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      Text(
                        S.of(context).discount,
                        style: TextStyle(
                          fontSize: 14,
                          color: Theme.of(context)
                              .colorScheme
                              .secondary
                              .withOpacity(0.8),
                        ),
                      ),
                      Text(
                        PriceTools.getCurrencyFormatted(
                          cartModel.getDiscountTotal(),
                          currencyRate,
                          currency: currency,
                        )!,
                        // cartModel.getCoupon(),
                        style:
                            Theme.of(context).textTheme.titleMedium!.copyWith(
                                  fontSize: 14,
                                  color: Theme.of(context)
                                      .colorScheme
                                      .secondary
                                      .withOpacity(0.8),
                                ),
                      )
                    ],
                  ),
                ),
              Services().widget.renderTaxes(taxModel, context),
              Services().widget.renderRewardInfo(context),
              Services().widget.renderCheckoutWalletInfo(context),
              Services().widget.renderCODExtraFee(context),
              Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    Text(
                      S.of(context).total,
                      style: TextStyle(
                          fontSize: 16,
                          color: Theme.of(context).colorScheme.secondary),
                    ),
                    Text(
                      PriceTools.getCurrencyFormatted(
                          cartModel.getTotal(), currencyRate,
                          currency: cartModel.currencyCode)!,
                      style: TextStyle(
                        fontSize: 20,
                        color: Theme.of(context).colorScheme.secondary,
                        fontWeight: FontWeight.w600,
                        decoration: TextDecoration.underline,
                      ),
                    )
                  ],
                ),
              ),
              const SizedBox(height: 20),
            ]
          ],
        ),
      ),
    );

    return ListenableProvider.value(
      value: paymentMethodModel,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (useDesktopLayout)
            Padding(padding: const EdgeInsets.only(bottom: 50), child: body)
          else
            Expanded(child: body),
          Consumer<PaymentMethodModel>(builder: (context, model, child) {
            return _buildBottom(model, cartModel);
          })
        ],
      ),
    );
  }

  Widget _buildBottom(
      PaymentMethodModel paymentMethodModel, CartModel cartModel) {
    final bgColor = Theme.of(context).primaryColor;
    printLog('CCCCCAPRODDD ${cartModel.productsInCart.keys.map((item) {
      var product = cartModel.getProductById(Product.cleanProductID(item));
      printLog('asfasfsaf ${product?.productCategory?.name}');

      printLog(
          'wwwwwrisInMarketCategffwfwories ${orderMarketSubSubCategories?.map((cat) => cat.name?.toLowerCase())}');
      printLog('www22323232 ${product?.productCategory?.name?.toLowerCase()}');
      printLog(
          'isInMarketCategories ${orderMarketSubSubCategories?.any((cat) => cat.name?.toLowerCase() == product?.productCategory?.name?.toLowerCase())}');
    }).toList()}');

    return CommonSafeArea(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (kPaymentConfig.enableShipping ||
              kPaymentConfig.enableAddress ||
              kPaymentConfig.enableReview) ...[
            SizedBox(
              width: 130,
              child: OutlinedButton(
                onPressed: () {
                  isPaying ? showSnackbar : widget.onBack!();
                },
                child: Text(
                  kPaymentConfig.enableReview
                      ? S.of(context).goBack.toUpperCase()
                      : kPaymentConfig.enableShipping
                          ? S.of(context).goBackToShipping
                          : S.of(context).goBackToAddress,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.secondary,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 8),
          ],
          if (widget.hideCheckout == false)
            Expanded(
              child: ButtonTheme(
                height: 45,
                child: ElevatedButton.icon(
                  style: ElevatedButton.styleFrom(
                    foregroundColor: Colors.white,
                    backgroundColor: bgColor,
                    elevation: 0,
                  ),
                  onPressed: (paymentMethodModel.message?.isNotEmpty ?? false)
                      ? null
                      : () => isPaying || selectedId == null
                          ? showSnackbar
                          : placeOrder(paymentMethodModel, cartModel),
                  icon: Icon(
                    CupertinoIcons.check_mark_circled_solid,
                    size: 20,
                    color: bgColor.getColorBasedOnBackground,
                  ),
                  label: Text(
                    S.of(context).placeMyOrder.toUpperCase(),
                    style: TextStyle(
                      color: bgColor.getColorBasedOnBackground,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
