import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../common/config.dart';
import '../../generated/l10n.dart';
import '../../models/app_model.dart';
import '../../screens/settings/layouts/mixins/setting_action_mixin.dart';
import 'web_layout_mixin.dart';
import 'widgets/download_app_widget.dart';
import 'widgets/my_account_widget.dart';

class FooterWeb extends StatefulWidget {
  const FooterWeb({super.key});

  @override
  State<FooterWeb> createState() => _FooterWebState();
}

class _FooterWebState extends State<FooterWeb>
    with WebLayoutMixin, SettingActionMixin {
  void _onTapOpenUrlWeb(String urlWeb) => onTapOpenUrl(context, urlWeb);

  @override
  Widget build(BuildContext context) {
    final appModel = Provider.of<AppModel>(context, listen: false);

    final themeConfig = appModel.themeConfig;

    return Container(
      height: 280,
      color: Theme.of(context).primaryColor.withOpacity(0.1),
      margin: const EdgeInsets.only(top: 10),
      padding: const EdgeInsets.symmetric(horizontal: 64.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: LayoutBuilder(
              builder: (_, constraints) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Row(
                            children: [
                              // FollowSocialWidget(
                              //   padding: EdgeInsets.zero,
                              //   title: const SizedBox(),
                              //   sizeIcon: 30,
                              //   onTap: _onTapOpenUrlWeb,
                              //   color: Theme.of(context)
                              //       .scaffoldBackgroundColor
                              //       .getColorBasedOnBackground,
                              // ),
                              if (canShowVendorLogo())
                                Padding(
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 20.0),
                                    child: CircleAvatar(
                                      backgroundColor: Colors.transparent,
                                      radius: 70,
                                      backgroundImage: NetworkImage(
                                          currentVendor?.logoUrl ??
                                              appModel.themeConfig.logo),
                                    )),

                              const Spacer(),

                              if (currentVendor == null ||
                                  currentVendor?.id == 1)
                                DownloadAppWidget(
                                  onTap: _onTapOpenUrlWeb,
                                ),
                            ],
                          ),
                        ),
                        // RichText(
                        //   text: TextSpan(
                        //     text: 'Create your free website now, with ',
                        //     style: const TextStyle(
                        //         color: Colors.black, fontSize: 20),
                        //     children: [
                        //       TextSpan(
                        //         text: 'Idea2App',
                        //         style: const TextStyle(
                        //           color: Colors.blue,
                        //           decoration: TextDecoration.underline,
                        //         ),
                        //         recognizer: TapGestureRecognizer()
                        //           ..onTap = () =>
                        //               _onTapOpenUrlWeb('https://idea2app.tech'),
                        //       ),
                        //     ],
                        //   ),
                        // ),
                      ],
                    ),
                    const MyAccountWidget(),
                  ],
                );
              },
            ),
          ),
          const Divider(),
          Row(
            children: [
              if (idea2AppCopyRightUrl.isNotEmpty) ...[
                Padding(
                  padding: const EdgeInsetsDirectional.only(start: 11)
                      .copyWith(bottom: 10),
                  child: InkWell(
                      onTap: () => onTapOpenUrl(context, idea2AppCopyRightUrl),
                      child: Text(S.of(context).copyright)),
                ),
                const Spacer(),
              ],
              if (privacyLink.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(bottom: 10),
                  child: InkWell(
                    hoverColor: Colors.transparent,
                    onTap: () => onTapOpenUrl(context, privacyLink
                        // context.read<AppModel>().appConfig?.settings.privacy ??
                        //     kAdvanceConfig.privacyPoliciesPageUrlOrId
                        ),
                    child: Text(
                      S.of(context).privacyTerms,
                      style: const TextStyle(),
                    ),
                  ),
                )
            ],
          ),
        ],
      ),
    );
  }
}
