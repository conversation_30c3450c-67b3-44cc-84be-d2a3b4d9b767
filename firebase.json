{"hosting": {"target": "idea2app-customer", "public": "build/web", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}, "flutter": {"platforms": {"android": {"default": {"projectId": "idea2app-dev", "appId": "1:163437334343:android:f3e76701f478f1e9b98662", "fileOutput": "android/app/google-services.json"}}, "ios": {"default": {"projectId": "idea2app-dev", "appId": "1:163437334343:ios:0ab0d1198f15698fb98662", "uploadDebugSymbols": false, "fileOutput": "ios/Runner/GoogleService-Info.plist"}}, "dart": {"lib/firebase_options.dart": {"projectId": "idea2app-dev", "configurations": {"android": "1:163437334343:android:f3e76701f478f1e9b98662", "ios": "1:163437334343:ios:0ab0d1198f15698fb98662"}}}}}}