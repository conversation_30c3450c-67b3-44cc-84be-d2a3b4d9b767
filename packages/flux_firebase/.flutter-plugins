# This is a generated file; do not edit or check into version control.
app_tracking_transparency=/Users/<USER>/.pub-cache/hosted/pub.dev/app_tracking_transparency-2.0.4/
cloud_firestore=/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.3/
cloud_firestore_web=/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_web-4.4.3/
devicelocale=/Users/<USER>/.pub-cache/hosted/pub.dev/devicelocale-0.7.0/
facebook_auth_desktop=/Users/<USER>/.pub-cache/hosted/pub.dev/facebook_auth_desktop-1.0.3/
file_selector_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.1+3/
file_selector_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.2/
file_selector_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.2/
firebase_analytics=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.4.2/
firebase_analytics_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.5.10+8/
firebase_auth=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.4.2/
firebase_auth_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_web-5.13.8/
firebase_core=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.11.0/
firebase_core_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.20.0/
firebase_dynamic_links=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.2/
firebase_messaging=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.2/
firebase_messaging_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.10.2/
firebase_remote_config=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_remote_config-5.4.0/
firebase_remote_config_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_remote_config_web-1.8.0/
flutter_direct_caller_plugin=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_direct_caller_plugin-0.0.4/
flutter_facebook_auth=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_facebook_auth-6.1.1/
flutter_facebook_auth_web=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_facebook_auth_web-5.0.0/
flutter_inappwebview=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/
flutter_inappwebview_android=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/
flutter_inappwebview_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/
flutter_inappwebview_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_macos-1.1.2/
flutter_inappwebview_web=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2/
flutter_inappwebview_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_windows-0.6.0/
flutter_localization=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_localization-0.2.0/
flutter_plugin_android_lifecycle=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.15/
flutter_secure_storage=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/
flutter_secure_storage_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.2/
flutter_secure_storage_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/
flutter_secure_storage_web=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/
flutter_secure_storage_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/
gms_check=/Users/<USER>/Flutter-Projects/Idea2App/idea2app_customer/packages/gms_check/
google_maps_flutter=/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter-2.10.0/
google_maps_flutter_android=/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_android-2.14.11/
google_maps_flutter_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.1/
google_maps_flutter_web=/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_web-0.5.10/
google_mobile_ads=/Users/<USER>/.pub-cache/hosted/pub.dev/google_mobile_ads-5.3.1/
google_sign_in=/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-6.2.1/
google_sign_in_android=/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-6.1.16/
google_sign_in_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.7.4/
google_sign_in_web=/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_web-0.12.0+2/
image_picker=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/
image_picker_android=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.7+4/
image_picker_for_web=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-2.2.0/
image_picker_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.8+1/
image_picker_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1/
image_picker_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1/
image_picker_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1/
in_app_update=/Users/<USER>/.pub-cache/hosted/pub.dev/in_app_update-4.2.2/
inspireui=/Users/<USER>/Flutter-Projects/Idea2App/idea2app_customer/packages/inspireui/
libphonenumber_plugin=/Users/<USER>/.pub-cache/hosted/pub.dev/libphonenumber_plugin-0.3.2/
libphonenumber_web=/Users/<USER>/.pub-cache/hosted/pub.dev/libphonenumber_web-0.3.1/
local_auth=/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth-2.3.0/
local_auth_android=/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.48/
local_auth_darwin=/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.3/
local_auth_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.8/
location=/Users/<USER>/.pub-cache/hosted/pub.dev/location-5.0.3/
location_web=/Users/<USER>/.pub-cache/hosted/pub.dev/location_web-4.2.0/
notification_permissions=/Users/<USER>/Flutter-Projects/Idea2App/idea2app_customer/packages/notification_permissions/
package_info_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.0.2/
path_provider=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/
path_provider_android=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.16/
path_provider_foundation=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/
path_provider_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.0/
path_provider_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.2.0/
permission_handler=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0/
permission_handler_android=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/
permission_handler_apple=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/
permission_handler_html=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.1/
permission_handler_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/
photo_manager=/Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.5.0/
qr_code_scanner_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/qr_code_scanner_plus-2.0.10+1/
rate_my_app=/Users/<USER>/.pub-cache/hosted/pub.dev/rate_my_app-2.0.0/
restart_app=/Users/<USER>/.pub-cache/hosted/pub.dev/restart_app-1.3.2/
shared_preferences=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.2.2/
shared_preferences_android=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.1.4/
shared_preferences_foundation=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.2.2/
shared_preferences_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.2.0/
shared_preferences_web=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.1.0/
shared_preferences_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.2.0/
sms_autofill=/Users/<USER>/.pub-cache/hosted/pub.dev/sms_autofill-2.3.1/
sqflite=/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.2.8+4/
the_apple_sign_in=/Users/<USER>/.pub-cache/hosted/pub.dev/the_apple_sign_in-1.1.1/
url_launcher=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/
url_launcher_android=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.0/
url_launcher_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/
url_launcher_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.1.1/
url_launcher_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.1.0/
url_launcher_web=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.3.3/
url_launcher_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.3/
video_player=/Users/<USER>/.pub-cache/hosted/pub.dev/video_player-2.9.3/
video_player_android=/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.4.9/
video_player_avfoundation=/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.5.6/
video_player_web=/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.3.4/
webview_flutter=/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter-4.10.0/
webview_flutter_android=/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-4.3.4/
webview_flutter_web=/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_web-0.2.3+2/
webview_flutter_wkwebview=/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.5/
youtube_player_iframe=/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_player_iframe-5.2.1/
youtube_player_iframe_web=/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_player_iframe_web-3.1.1/
